module.exports = {
  PLS_BOOKING_REFERENCE_NUMBER_PREFIX: 'PXL',
  NUMBER_OF_DIGITS_GENERATED_NUMBER: '000000',
  printLabelType: {
    PARXL_LABEL: 'PARXL',
    LMD_LABEL: 'LMD',
    BOTH_LABEL: 'BOTH',
    GENERIC_LABEL: 'GENERIC'
  },
  parcelStatus: {
    booked: 'booked',
    cancelled: 'cancelled',
    expired: 'expired',
    lmd_receive_booking: 'lmd_receive_booking',
    lmd_reject_booking: 'lmd_reject_booking',
    pending_pickup: 'pending_pickup',
    received_at_warehouse: 'received_at_warehouse',
    rejected_at_warehouse: 'rejected_at_warehouse',
    sorted: 'sorted',
    packed_to_gaylord: 'packed_to_gaylord',
    gaylord_ready_to_close: 'gaylord_ready_to_close',
    close_gaylord: 'close_gaylord',
    fwb_received: 'fwb_received',
    rcs_received_by_airline: 'rcs_received_by_airline',
    dep_from_origin_airport: 'dep_from_origin_airport',
    dep_from_transit_airport: 'dep_from_transit_airport',
    arr_at_transit_airport: 'arr_at_transit_airport',
    arr_at_destination_airport: 'arr_at_destination_airport',
    dlv_shipment_handed_over_to_consignee: 'dlv_shipment_handed_over_to_consignee',
    arrived_at_destination_custom_facility: 'arrived_at_destination_custom_facility',
    custom_cleared: 'custom_cleared',
    enroute_to_destination_sorting_hub: 'enroute_to_destination_sorting_hub',
    arrived_and_processing_at_sorting_hub: 'arrived_and_processing_at_sorting_hub',
    on_vehicle_for_delivery: 'on_vehicle_for_delivery',
    successful_delivery: 'successful_delivery',
    delivery_unsuccessful: 'delivery_unsuccessful',
    return_to_sq_triggered: 'return_to_sq_triggered',
    returned_to_sq: 'returned_to_sq',
    parcel_collected: 'parcel_collected',
    parcel_returned_by_recipient: 'parcel_returned_by_recipient',
    awaiting_merchant_pickup: 'awaiting_merchant_pickup',
    disposed: 'disposed',
    damaged_at_destination: 'damaged_at_destination',
    returned_to_merchant: 'returned_to_merchant',
    final_unsuccessful_delivery: 'final_unsuccessful_delivery',
    in_transit: 'in_transit',
    // Merchant old internal statues for mapping
    notified_customs_lmd: 'Notified Customs and LMD',
    rejected_at_booking: 'Rejected at Booking',
    cn38_closed: 'cn38_closed',
    mawb_closed: 'mawb_closed',
    hold_cancelled: 'hold_cancelled',
    hold_split_parcel: 'hold_split_parcel',
    split_parcel_created: 'split_parcel_created',
    split_parcel_completed_processing: 'split_parcel_completed_processing',
  },
  surchargeEnum: {
    status: {
      pending_for_approval: 'Pending Approval',
      approved: 'Approved',
      rejected: 'Rejected'
    },
    objectType: {
      fuelSurcharge: 'fuel_surcharge',
    }
  },
  shipmentTypes: {
    domestic: 'domestic',
    international: 'international',
    return_domestic: 'return-domestic',
    return_international: 'return-international'
  },
  approval_status: {
    created: 'Created',
    pending_for_approval: 'Pending for Approval',
    approved: 'Approved',
    rejected: 'Rejected',
    withdrawn: 'Withdrawn'
  },
  bookingPlatform: {
    api: 'API',
    newMerchantPortal: 'MP',
    shopify: 'SHOPIFY'
  },
  weightUnits: {
    KG: 'kg',
    LB: 'lb'
  },
  dimensionUnits: {
    IN: 'in',
    CM: 'cm'
  },
  confirmOptions: {
    YES: 'Y',
    NO: 'N'
  },
  serviceOptions: {
    SELF_COLLECT: 'self-collect',
    STD: 'standard',
    PLUS: 'plus',
    POSTAL: 'postal',
    B2B: 'b2b'
  },
  incoterms: {
    NA: 'NA',
    DDP: 'DDP',
    DDU: 'DDU'
  },
  notificationTypes: {
    BOOKED: 'booked',
    BOOKING_ERROR: 'booking_error',
    VALIDATION_ERROR: 'validation_error',
    LABEL: 'label',
    CSV_BOOKING_REPORT: 'csv_booking_report',
    MULTI_PIECE_PROCESS_COMPLETED: 'multi_piece_process_completed',
  },
  cacheName: {
    DESTINATION: 'destinationV2',
    COUNTRIES: 'countries',
    TIMEZONE_PREFIX: 'timezone_',
    SYS_CONFIG: 'SystemConfigurations',
    PARCEL_STATUSES: 'parcelStatuses',
    COLLECTION_POINTS: 'collection_points',
    NJV_PUDO_POINTS_SG: 'NJV_PUDO_points_SG',
    NJV_PUDO_POINTS_MY: 'NJV_PUDO_points_MY',
    OP_HUBS: 'op_hubs',
    HS_Code_AU: 'hscodeAU',
    HS_Code_NZ: 'hscodeNZ',
    HS_Code_Blacklist: 'hscodeBlacklist',
    MERCHANT: 'MERCHANT',
    HS_CODE_MASTER_LIST: 'hscodeMasterListMapping',
  },
  categoryStt: {
    COMPLETED: 'COMPLETED',
    IN_PROGRESS: 'IN_PROGRESS',
    NEEDS_ATTENTION: 'NEEDS_ATTENTION'
  },
  LMD_SERVICE: {
    AUS_POST: ['Australia Post'],
    NZ_POST: ['New Zealand Post'],
    KERRY_HK: 'Kerry Hong Kong',
    EVRI: 'EVRi UK'
  },
  lmdProviders: {
    JANIO: 'Janio',
    KERRY: 'Kerry',
    BOXC: 'BoxC',
    BOXC_JAPAN: 'BoxC Japan',
    NINJAVAN_MALAYSIA: 'Ninjavan Malaysia',
    NINJAVAN_SINGAPORE: 'Ninjavan Singapore',
    NINJAVAN_PHILIPPINES: 'Ninjavan Philippines',
    HANJIN_KOREA: 'Hanjin',
    SAGAWA_JAPAN: 'Sagawa Japan',
    KERRY_THAILAND: 'Kerry', // Specific to Kerry Thai Lan only
    WMG_TW: 'World Marketing Group Taiwan',
    WMG_KR: 'World Marketing Group Korea',
    WMG_MY: 'World Marketing Group Malaysia',
    WMG_ID: 'World Marketing Group Indonesia',
    WMG_TH: 'World Marketing Group Thailand',
  },
  recipientDocumentType: ['LOCALTAXID', 'KTP', 'PASSPORT', 'LICENSE'],
  categories: ['Fashion Accessories', 'Fashion Footwear', 'Fashion Apparels (Men)', 'Fashion Apparels (Women)',
    'Fashion Apparels (Babies, Toddlers and Children)', 'Fashion Apparel',
    'Electronics', 'Electronics (Non-Telecommunications)',
    'Electronics (Telecommunications)', 'Lifestyle Products',
    'Lifestyle (Health Related)', 'Lifestyle (Beauty Related)',
    'Lifestyle (Home & Living)', 'Lifestyle (Hobbies & Collection)',
    'Lifestyle (Pantry & Packaged Food & Beverages)',
    'Others', 'Printed Matters',
  ],
  functionName: {
    BOOKING: 'BOOKING',
    DOWNLOAD_LABEL: 'DownloadLabel',
    WEBHOOK: 'MerchantWebhook',
    AZ_STORAGE_QUEUE: 'AZ_STORAGE_QUEUE',
    ESTIMATE_RATE: 'ESTIMATE_RATE',
  },
  versionConfigurations: {
    NON_GST_REGISTERED_SINGAPORE: 'NON_GST_REGISTERED_SINGAPORE',
    DEMINIMIS_SIN_LIMIT: 'DEMINIMIS_SIN_LIMIT',
    HS_CODE_OVERWRITING_AUNZ: 'HS_CODE_OVERWRITING_AUNZ',
    NJV_TID: 'NJV_TID',
  },
  version: {
    TRUE: 'TRUE',
    TRUE_V1: 'TRUE-V1',
    TRUE_V2: 'TRUE-V2',
    FALSE: 'FALSE',
    V1: 'V1',
    V2: 'V2',
    V3: 'V3',
  },
  invoiceType: {
    PD: 'parcelDelivery',
    DT: 'dutiesAndTaxes',
    DTv2: 'dutiesAndTaxesV2'
  },
  currency: {
    SGD: 'SGD',
    PHP: 'PHP',
    MYR: 'MYR',
    JPY: 'JPY'
  },
  country: {
    Singapore: 'Singapore',
    Philippines: 'Philippines',
    Malaysia: 'Malaysia',
    Japan: 'Japan'
  },
  redisKey: {
    EXCHANGE_RATE: 'exchangeRate'
  },
  CAPACITY: {
    UNLIMITED: 'UNLIMITED'
  },
  POST_CODE_TYPE: {
    NATIONWIDE: 'nationwide',
    DIGIT: 'digit',
    RANGE: 'range',
    LIST: 'list',
  },
  BLOB_CONTAINER_NAME: {
    RATE_ZONE: 'ratezone'
  },
  appName: 'MERCHANT-API',
  TAX_RATE_TYPE: {
    DE_MINIMIS: 'de_minimis',
  },
  UNASSIGNNED_ZONE: 'Unassigned',
  DE_MINIMIS_TIER: {
    A: 'A', //High value
    B: 'B' //Low value
  },
  DUMMY_EMAIL: '<EMAIL>',
  DE_MINIMIS: {
    DEFAULT: 400
  },
  MAWB_STATUS: {
    FWB: 'FWB',
    RCS: 'RCS',
    DEP: 'DEP',
    DEP_TRANSIT: 'DEP_TRANSIT',
    ARR_TRANSIT: 'ARR_TRANSIT',
    ARR: 'ARR',
    DLV: 'DLV',
  },
  MERCHANT_TYPE: {
    NORMAL: 'normal',
    CATEGORY_1: 'category_1'
  },
  SOCKET_EVENTS: {
    AUTH: 'APP:AUTH',
    NOTIFICATION_PUSH: 'NOTIFICATION:PUSH',
  },
  EXT: {
    PDF: '.pdf',
    CSV: '.csv',
    XLSX: '.xlsx',
    ZIP: '.zip',
    TXT: '.txt',
    JSON: '.json',
  },
  DATE_FORMAT: {
    MONTH_YEAR: 'MMM YYYY',
    YEAR_MONTH_DAY: 'YYYY-MM-DD'
  }
};
