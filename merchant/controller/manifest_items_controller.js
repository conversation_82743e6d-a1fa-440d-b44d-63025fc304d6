const aesUtils = require('../models/aesUtils');
const { parcelStatus } = require('../const/enum');
const _ = require('lodash');
const ManifestItemService = require('../services/manifest-item-services');
const mawbService = require('../services/mawb-service');
const StatusMappingService = require('../services/statusMappingService');
const { formatBatchNo } = require('../report/report.utils');
const ShipmentService = require('../services/shipment-services');
const logger = require('../utilities/logUtils');

function ManifestItemsController(manifestItemDao, merchantDao, operationHubDao, gaylordDao) {
  this.manifestItemDao = manifestItemDao;
  this.merchantDao = merchantDao;
  this.operationHubDao = operationHubDao;
  this.gaylordDao = gaylordDao;
}

module.exports = ManifestItemsController;
ManifestItemsController.prototype = {
  
  getParcelByTrackingID: async function (req, res) {
    try {
      const tracking_id = req.params.tracking_id;
      const merchant = req.merchant;

      const querySpecFindParcel = {
        query: `SELECT * FROM c WHERE (c.id = @tracking_id OR c.tracking_id = @tracking_id 
          OR c.tracking_no = @tracking_id) AND c.merchant_name = @merchant_name`,
        parameters: [{
          name: '@tracking_id',
          value: tracking_id
        }, {
          name: '@merchant_name',
          value: merchant.merchant_name
        }]
      };

      let parcel;
      const items = await this.manifestItemDao.find(querySpecFindParcel);
      if (!items.length) {
        console.log('<manifest_items_controller> - <getParcelByTrackingID> - db error when find parcel');
        res.status(404).json({
          message: 'Parcel not found',
          description: 'Parcel that you are finding was not found'
        });
        return;
      }
    
      const sortArray = _.orderBy(items, ['order_date'], ['desc']);
      parcel = sortArray[0];

      let allowSplitParcel = false;

      let gaylord;
      let mawbs = [];

      if (parcel.gaylord_no) {
        gaylord = await this.gaylordDao.getItem(parcel.gaylord_no);
        mawbs = await mawbService.findByNoAndId(gaylord.mawb_no);
        parcel.flightNos = ShipmentService.getFlightNo(gaylord, mawbs);
      }

      let hubLocation = '';
      let originCountry = '';
      const operationHub = await this.getOperationHub(parcel.operation_hub);

      if (operationHub) {
        const city_suburb = aesUtils.CrtCounterDecrypt(operationHub.city_suburb);
        originCountry = aesUtils.CrtCounterDecrypt(operationHub.country);
        hubLocation = `${city_suburb}, ${originCountry}`;
      }

      const filteredNotShowStatus = parcel.tracking_status
        .filter((stt) => StatusMappingService.isMerchantVisible(stt.status));
      
      if (!StatusMappingService.isMerchantVisible(parcel.latest_tracking_status)) {
        parcel.latest_tracking_status = filteredNotShowStatus.at(-1).status;
      }
        
      parcel.tracking_status = filteredNotShowStatus.map((item) => {
        const externalStatus =
            StatusMappingService.getInternal2ExternalStatus().find(
              (stat) =>
                String(stat.pls).toUpperCase() ===
                String(item.status).toUpperCase()
            );
        item.location = this.getLocation(
          item.status,
          hubLocation,
          originCountry,
          aesUtils.CrtCounterDecrypt(parcel.country)
        );
        item.status = externalStatus ? externalStatus.bc : item.status;
        return item;
      });
      parcel.tracking_status.sort((a, b) => {
        return new Date(a.date) - new Date(b.date);
      });
      const lmdReceiveManifestStatus = StatusMappingService
        .getManifestStatus(parcelStatus.lmd_receive_booking).toLowerCase();
      const bookedManifestStatus = StatusMappingService.getManifestStatus(parcelStatus.booked).toLowerCase();

      if (
        merchant.allow_multi_piece_shipment &&
        [lmdReceiveManifestStatus, bookedManifestStatus].includes(parcel.latest_tracking_status.toLowerCase()) &&
        parcel.parent_id === undefined
      ) {
        allowSplitParcel = true;
      }

      parcel.country = aesUtils.CrtCounterDecrypt(parcel.country);
      parcel.recipient_first_name = aesUtils.CrtCounterDecrypt(parcel.recipient_first_name);
      parcel.recipient_last_name = aesUtils.CrtCounterDecrypt(parcel.recipient_last_name);
      parcel.recipient_addressline1 = aesUtils.CrtCounterDecrypt(parcel.recipient_addressline1);
      parcel.recipient_addressline2 = aesUtils.CrtCounterDecrypt(parcel.recipient_addressline2);
      parcel.recipient_addressline3 = aesUtils.CrtCounterDecrypt(parcel.recipient_addressline3);
      parcel.city_suburb = aesUtils.CrtCounterDecrypt(parcel.city_suburb);
      parcel.merchant_name = aesUtils.CrtCounterDecrypt(parcel.merchant_name);
      parcel.postcode = aesUtils.CrtCounterDecrypt(parcel.postcode);
      parcel.email = aesUtils.CrtCounterDecrypt(parcel.email);
      parcel.phone = aesUtils.CrtCounterDecrypt(parcel.phone);
      parcel.phone_country_code = aesUtils.CrtCounterDecrypt(parcel.phone_country_code);
      parcel.state = aesUtils.CrtCounterDecrypt(parcel.state);
      parcel.new_batch_no = formatBatchNo(parcel.PLS_batch_no);
      parcel.printable = StatusMappingService.isPrintable(parcel.latest_tracking_status);
      parcel.pickup_required = parcel.pickup_required === 'Y' ? 'Yes' : 'No';
      parcel.shipment_notification = !parcel.shipment_notification ? 'No' : 'Yes';
      const {blockchain, categoryStt} = ShipmentService.getMerchantLatestStatus(parcel, mawbs);
      parcel.merchantStatus = blockchain;
      parcel.shorten = StatusMappingService.getShortenStatus(parcel.latest_tracking_status);
      parcel.categoryStt = categoryStt;
      parcel.isCancellable = StatusMappingService.isCancellable(parcel.latest_tracking_status);
      parcel.allowSplitParcel = allowSplitParcel;
      res.status(200).json({ parcel });
    } catch (err) {
      res.status(404).json({
        message: 'Parcel not found',
        description: 'Parcel that you are finding was not found'
      });
    }
  },

  getLocation: function (status, hubLocation, originCountry, destinationCountry) {
    status = status.toLowerCase();

    let hubStatuses = [
      StatusMappingService.getManifestStatus(parcelStatus.received_at_warehouse),
      StatusMappingService.getManifestStatus(parcelStatus.rejected_at_warehouse),
      StatusMappingService.getManifestStatus(parcelStatus.sorted),
      StatusMappingService.getManifestStatus(parcelStatus.packed_to_gaylord),
    ];
    hubStatuses = hubStatuses.map(stt => stt.toLowerCase());

    let destinationStatuses = [
      StatusMappingService.getManifestStatus(parcelStatus.arrived_and_processing_at_sorting_hub),
      StatusMappingService.getManifestStatus(parcelStatus.on_vehicle_for_delivery),
      StatusMappingService.getManifestStatus(parcelStatus.successful_delivery),
      StatusMappingService.getManifestStatus(parcelStatus.delivery_unsuccessful),
      StatusMappingService.getManifestStatus(parcelStatus.parcel_collected),
      StatusMappingService.getManifestStatus(parcelStatus.arr_at_destination_airport),
    ];
    destinationStatuses = destinationStatuses.map(stt => stt.toLowerCase());

    let originStatuses = [
      StatusMappingService.getManifestStatus(parcelStatus.dep_from_origin_airport)
    ];
    originStatuses =  originStatuses.map(stt => stt.toLowerCase());

    if (_.intersection([status], hubStatuses).length > 0) {
      return hubLocation;
    } else if (_.intersection([status], destinationStatuses).length > 0) {
      return destinationCountry;
    } else if(_.intersection([status], originStatuses).length > 0) {
      return originCountry;
    }
    return '';
  },

  getOperationHub: async function (operation_hub_id) {
    try {
      const operationHub = await this.operationHubDao.getItemByField({ name: 'operation_hub', value: operation_hub_id});
      if (!operationHub) {
        return '';
      }
      return operationHub;
    } catch (err) {
      console.log(`error getting operation hub: ${operation_hub_id}`, err);
    }
  },

  async searchParcels(req, res) {
    try {
      const { keyword } = req.query;
      const { merchant_name } = req.merchant;
      const parcels = await ManifestItemService.searchParcels(keyword, merchant_name);
      const sortArray = _.orderBy(parcels, ['order_date'], ['desc']);
      let data = _.uniqBy(sortArray, 'tracking_id');
      data = data.map(e => Object.assign({...e, keyword: keyword}));
      res.json({
        requestSuccess: true,
        data
      });
    } catch (error) {
      res.status(402).json({
        requestSuccess: false,
        message: error
      });
    }
  },

  getDashboardData: async function (req, res) {
    try {
      const { merchant_name } = req.merchant;
      const sixMonthsData = await ManifestItemService.queryMPDashboardData(merchant_name);
      const processedData = await ManifestItemService.processDashboardData(sixMonthsData);
      
      return res.status(200).json({
        success: true,
        data: processedData
      });
    } catch (error) {
      logger.error('manifest_items_controller getDashboardData', error);
      return res.status(500).json({
        success: false,
        message: error.message || 'Error occurred while retrieving dashboard data'
      });
    }
  }
};
