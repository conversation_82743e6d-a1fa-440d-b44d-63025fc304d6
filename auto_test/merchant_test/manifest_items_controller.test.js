const ManifestItemsController = require('../../merchant/controller/manifest_items_controller');
const operationHubMock = require("../operation_hub_mock/aus01");
jest.mock('./../../merchant/services/keyvault-service')
const mawbService = require('../../merchant/services/mawb-service');
const StatusMappingService = require('../../merchant/services/statusMappingService');
const ManifestItemService = require('../../merchant/services/manifest-item-services');

const mockFind = jest.fn();
const manifestItemDao = { find: jest.fn(), getItem: jest.fn(), updateItemResolveConflict: mockFind };
const merchantDao = {find: jest.fn()};
const operationHubDao = { find: jest.fn() };
const gaylordDao = jest.fn();
const ShipmentService = require('../../merchant/services/shipment-services');
console.log = jest.fn();
console.error = jest.fn();

const manifest_items_controller = new ManifestItemsController(
  manifestItemDao,
  merchantDao,
  operationHubDao,
  gaylordDao
);

beforeEach(() => {
  jest.clearAllMocks();
});

describe("test operation hub", () => {
    const req = {
      params: { tracking_id: "tracking_id" },
      decoded: {
        emails: ["<EMAIL>"]
      },
      merchant: {
        merchant_name: "merchant_name_1"
      }
    };
    const jsonFn = jest.fn();
    const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
    };
    test("Get operation hub successfully", async () => {
      manifest_items_controller.manifestItemDao.find = jest.fn().mockResolvedValueOnce([{ operation_hub: "LAX01" }]);
      manifest_items_controller.operationHubDao.getItemByField = jest.fn().mockResolvedValueOnce([{operationHubMock}]);
      await manifest_items_controller.getParcelByTrackingID(req, res);
      expect(manifest_items_controller.operationHubDao.getItemByField).toHaveBeenCalled();
    });
  
    test("Get operation hub failed", async () => {
      const errorMessage = new Error('Error');
      manifest_items_controller.operationHubDao.getItemByField = jest.fn().mockRejectedValue(errorMessage);
      try {
        await manifest_items_controller.getParcelByTrackingID(req, res);
      } catch (err) {
        expect(err).toBe(errorMessage);
      }
    });
  
    test("Get location by hub's status", async () => {
      let locationOfArrival = "Singapore, Singapore";
      const results = manifest_items_controller.getLocation(
        "Received at Warehouse",
        locationOfArrival, "Vietnam", "Singapore"
      );
      expect(results).toEqual(locationOfArrival);
    });
  
    test("Get location by origin's status", async () => {
      let country = "Singapore";
      const results = manifest_items_controller.getLocation(
        "Departed from origin airport",
        "Singapore, Singapore", country, "Vietnam"
      );
      expect(results).toEqual(country);
    });

    test("Get location by destination's status", async () => {
        let country = "Singapore";
        const results = manifest_items_controller.getLocation(
          "On vehicle for delivery",
          "Singapore, Singapore", "Vietnam", country
        );
        expect(results).toEqual(country);
      });
  
    test("Get location of other time", async () => {
      let country = "";
      const results = manifest_items_controller.getLocation(
        "Booked",
        "Singapore, Singapore",
        "Vietnam",
        "USA",
      );
      expect(results).toEqual(country);
    });
  });

describe('should test function get parcel detail', () => {
  // prepare mock function
  const req = {
    params: { tracking_id: 'tracking_id' },
    decoded: {
      emails: ['<EMAIL>']
    },
    merchant: {
        merchant_name: 'merchant_name_1'
    }
  };
  manifest_items_controller.merchantDao = {find: jest.fn()}
//   manifest_items_controller.manifestItemDao = {find: jest.fn()}
  const jsonFn = jest.fn();
  const res = { 
      json: jsonFn,
      status: jest.fn().mockReturnValue({json: jsonFn})
  };
  StatusMappingService.isMerchantVisible = jest.fn().mockReturnValue(true);

  test('DAO should be called with correct params', async () => {
    let querySpecFindParcel = {
        query: `SELECT * FROM c WHERE (c.id = @tracking_id OR c.tracking_id = @tracking_id 
          OR c.tracking_no = @tracking_id) AND c.merchant_name = @merchant_name`,
        parameters: [{
            name: '@tracking_id',
            value: req.params.tracking_id
        }, {
            name: '@merchant_name',
            value: req.merchant.merchant_name
        }]
    };


    manifest_items_controller.merchantDao.find.mockResolvedValueOnce([{item:''}]);
    manifest_items_controller.manifestItemDao.find.mockResolvedValueOnce([{item:''}]);
    await manifest_items_controller.getParcelByTrackingID(req, res);

    expect(manifest_items_controller.manifestItemDao.find.mock.calls[0][0]).toEqual(querySpecFindParcel);
  });

  test('should catch error', async () => {
    let error_1 = new Error('test 1');

    manifest_items_controller.manifestItemDao.find.mockRejectedValueOnce(error_1);
    const res_1 = Object.assign({}, res)
    try {
        await manifest_items_controller.getParcelByTrackingID(req, res);
    } catch (err) {
        expect(console.log).toHaveBeenNthCalledWith(2, '<manifest_items_controller> - <getParcelByTrackingID> - db error when find parcel: ', error_1);
        expect(res_1.status).toHaveBeenCalledWith(404);
        expect(res_1.status().json).toHaveBeenCalledWith({ message: "Parcel not found", description: "Parcel that you are finding was not found" });
    }

    manifest_items_controller.manifestItemDao.find.mockResolvedValueOnce([]);
    const res_2 = Object.assign({}, res)
    await manifest_items_controller.getParcelByTrackingID(req, res_2);

    expect(res_2.status).toHaveBeenCalledWith(404);
    expect(res_2.status().json).toHaveBeenCalledWith({ message: "Parcel not found", description: "Parcel that you are finding was not found" });
  });

  test('should return parcel if all conditions are valid', async () => {
    const parcel = {merchant_name: 'merchant_name_1', id: 'parcel_1', tracking_status: []};
    manifest_items_controller.manifestItemDao.find.mockResolvedValueOnce([parcel]);
    StatusMappingService.getShortenStatus = jest.fn();
    ShipmentService.getMerchantLatestStatus = jest.fn().mockReturnValue({ bcStt: '', category: '' });
    await manifest_items_controller.getParcelByTrackingID(req, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.status().json).toHaveBeenCalledWith({ parcel });
  })

  test('should response 403 if not a merchant', async () => {
    req.merchant = null;
    manifest_items_controller.merchantDao.find.mockResolvedValueOnce([]);
    await manifest_items_controller.getParcelByTrackingID(req, res);
    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.status().json).toHaveBeenCalledWith({description: "Parcel that you are finding was not found", message: "Parcel not found"});
  });

  test('PLS-1208 should return parcel if all conditions are valid', async () => {
    const parcel = require("../manifest_item_mock/manifest_item_mock").manifest_item_pls_1650;
    const mawb = require("../mawb_mock/mawb_mock_ref_id");
    manifest_items_controller.manifestItemDao.find.mockResolvedValueOnce([parcel]);
    gaylordDao.getItem = jest.fn().mockResolvedValue({});
    mawbService.findByNoAndId = jest.fn().mockResolvedValueOnce(mawb);
    manifest_items_controller.getLocation = jest.fn().mockReturnValue("Australia");
    const my_req = {
        params: { tracking_id: 'tracking_id' },
        decoded: {
          emails: ['<EMAIL>']
        },
        merchant: {
            merchant_name: 'merchant_name_1'
        }
      };
    //Act
    await manifest_items_controller.getParcelByTrackingID(my_req, res);

    //Expect
    expect(res.status).toHaveBeenCalledWith(200);
    expect(parcel.tracking_status[0]).toEqual({
        "date": "2019-12-06T06:25:49.473Z",
        "status": "BOOKED",
        "location": "Australia"
    })
    expect(parcel.tracking_status[1]).toEqual({
        "date": "2019-12-06T06:26:13.319Z",
        "status": "BOOKING_RECEIVED",
        "location": "Australia"
    })
    expect(parcel.tracking_status[2]).toEqual({
        "date": "2019-12-06T06:44:30.938Z",
        "status": "PROCESSING_AT_PARXL_EXPORT_HUB",
        "location": "Australia"
    })
    expect(parcel.tracking_status[3]).toEqual({
        "date": "2019-12-06T06:47:53.526Z",
        "gaylord_no": "GL-19-00000153",
        "status": "PROCESSING_AT_PARXL_EXPORT_HUB",
        "location": "Australia"
    })
    expect(res.status().json).toHaveBeenCalledWith({ parcel });
  })
})

describe('getDashboardData', () => {
  const req = {
    merchant: {
      merchant_name: 'test_merchant'
    }
  };
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should successfully get and process dashboard data', async () => {
    // Mock data
    const mockDashboardData = [
      { month: '2024-01', count: 10 },
      { month: '2024-02', count: 15 }
    ];
    const mockProcessedData = [
      { month: '2024-01', count: 10, monthName: 'January' },
      { month: '2024-02', count: 15, monthName: 'February' }
    ];

    // Mock service calls
    ManifestItemService.queryMPDashboardData = jest.fn().mockResolvedValue(mockDashboardData);
    ManifestItemService.processDashboardData = jest.fn().mockResolvedValue(mockProcessedData);

    // Execute
    await manifest_items_controller.getDashboardData(req, res);

    // Verify
    expect(ManifestItemService.queryMPDashboardData).toHaveBeenCalledWith('test_merchant');
    expect(ManifestItemService.processDashboardData).toHaveBeenCalledWith(mockDashboardData);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      success: true,
      data: mockProcessedData
    });
  });

  test('should handle errors and return 500 status', async () => {
    // Mock error
    const mockError = new Error('Database error');
    ManifestItemService.queryMPDashboardData = jest.fn().mockRejectedValue(mockError);

    // Execute
    await manifest_items_controller.getDashboardData(req, res);

    // Verify
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Database error'
    });
  });

  test('should handle custom error message', async () => {
    // Mock error with custom message
    const mockError = new Error('Custom error message');
    ManifestItemService.queryMPDashboardData = jest.fn().mockRejectedValue(mockError);

    // Execute
    await manifest_items_controller.getDashboardData(req, res);

    // Verify
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Custom error message'
    });
  });
});
